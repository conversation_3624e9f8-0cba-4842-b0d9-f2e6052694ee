// =================================================================================================
// File: /prisma_ai/src/prisma_ui/services/auth_service.rs
// =================================================================================================
// Purpose: Authentication service for user management and JWT-based security across multiple devices.
// This service handles user registration, login, logout, JWT token generation/validation, and
// role-based permissions for the PRISMA UI system. It supports multi-device authentication
// with session coordination and secure credential management.
// =================================================================================================
// Internal Dependencies:
// - session_service.rs: Session management integration for authenticated users
// - cache_service.rs: Authentication token caching and user data optimization
// - types.rs: Authentication data types and user credential structures
// - traits.rs: Authentication service trait definitions and abstractions
// - ../middleware/auth_middleware.rs: JWT validation middleware integration
// =================================================================================================
// External Dependencies:
// - SurrealDB: User credential storage, role management, and authentication history
// - JWT (jsonwebtoken): Token generation, validation, and expiration management
// - bcrypt: Password hashing and verification for secure credential storage
// - RabbitMQ: Authentication event publishing and multi-device coordination
// - uuid: Unique identifier generation for users, sessions, and authentication tokens
// =================================================================================================
// Module Interactions:
// - Integrates with session service for authenticated user session management
// - Coordinates with middleware layer for JWT validation and authentication checks
// - Manages user credential storage and retrieval via SurrealDB integration
// - Publishes authentication events via RabbitMQ for real-time UI updates
// - Provides authentication context to all UI services requiring user verification
// =================================================================================================
// Authentication Features:
// - User Management: Registration, login, logout, and profile management operations
// - JWT Security: Token generation, validation, refresh, and expiration handling
// - Multi-device Support: Device registration, authentication, and session coordination
// - Role-based Access: User roles, permissions, and access control management
// - Security: Password hashing, token encryption, and secure credential handling
// =================================================================================================
// Authentication Architecture:
// ┌─────────────────┐    Auth Requests ┌──────────────────────┐    Validation    ┌─────────────────┐
// │ Flutter UI      │ ──────────→      │ Authentication       │ ──────────→      │ Security        │
// │ (Multi-device)  │                  │ Service              │                  │ - JWT Tokens    │
// │ - Login Forms   │                  │ ├─User Management    │                  │ - Password Hash │
// │ - Registration  │                  │ ├─JWT Generation     │                  │ - Role Checking │
// │ - Device Auth   │                  │ ├─Multi-device Sync  │                  │ - Session Valid │
// └─────────────────┘                  │ └─Security Validation│                  └─────────────────┘
//                                      └──────────────────────┘                           ↓
//                                               ↓                                  ┌─────────────────┐
//                                      ┌──────────────────────┐                   │ Storage         │
//                                      │ Session & Cache      │ ←─────────────────│ - SurrealDB     │
//                                      │ Integration          │                   │ - User Data     │
//                                      └──────────────────────┘                   │ - Auth History  │
//                                                                                 └─────────────────┘
// =================================================================================================

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use jsonwebtoken::{encode, decode, Header, Algorithm, Validation, EncodingKey, DecodingKey};
use bcrypt::{hash, verify, DEFAULT_COST};
use tracing::{info, error, debug};

// Import error handling
use crate::err::{PrismaResult, GenericError as PrismaError, DomainError};

// Import storage
use crate::storage::{Database, DataStore};

// Custom serialization for datetime to ensure compatibility with SurrealDB
mod datetime_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // Format as RFC3339 for compatibility with SurrealDB
        serializer.serialize_str(&dt.to_rfc3339())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        DateTime::parse_from_rfc3339(&s)
            .map(|dt| dt.with_timezone(&Utc))
            .map_err(serde::de::Error::custom)
    }
}

// Separate module for Optional datetime fields
mod datetime_option_format {
    use chrono::{DateTime, Utc};
    use serde::{self, Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(dt: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match dt {
            Some(dt) => serializer.serialize_str(&dt.to_rfc3339()),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = Option::<String>::deserialize(deserializer)?;
        match s {
            Some(s) => {
                DateTime::parse_from_rfc3339(&s)
                    .map(|dt| Some(dt.with_timezone(&Utc)))
                    .map_err(serde::de::Error::custom)
            }
            None => Ok(None),
        }
    }
}

/// User data structure for database storage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    #[serde(with = "crate::storage::types::thing_id_string")]
    pub id: String,
    pub email: String,
    pub password_hash: String,
    pub role: String, // Store as string for SurrealDB compatibility
    pub is_active: bool,
    pub email_verified: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_format")]
    pub updated_at: DateTime<Utc>,
    #[serde(with = "datetime_option_format")]
    pub last_login: Option<DateTime<Utc>>,
}

/// User roles for access control
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum UserRole {
    Admin,
    User,
    Guest,
}

/// JWT claims structure
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // Subject (user ID)
    pub email: String,    // User email
    pub role: UserRole,   // User role
    pub exp: i64,         // Expiration time
    pub iat: i64,         // Issued at
    pub device_id: Option<String>, // Device identifier for multi-device support
}

/// Authentication request for login
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub email: String,
    pub password: String,
    pub device_id: Option<String>,
}

/// User registration request
#[derive(Debug, Deserialize)]
pub struct RegisterRequest {
    pub email: String,
    pub password: String,
    pub device_id: Option<String>,
}

/// Authentication response with JWT token
#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub token: String,
    pub refresh_token: String,
    pub user: UserInfo,
    #[serde(with = "datetime_format")]
    pub expires_at: DateTime<Utc>,
}

/// User information for responses (without sensitive data)
#[derive(Debug, Serialize, Deserialize)]
pub struct UserInfo {
    #[serde(with = "crate::storage::types::thing_id_string")]
    pub id: String,
    pub email: String,
    pub role: String, // Store as string for SurrealDB compatibility
    pub is_active: bool,
    pub email_verified: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "datetime_option_format")]
    pub last_login: Option<DateTime<Utc>>,
}

/// Device information for multi-device support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Device {
    #[serde(with = "crate::storage::types::thing_id_string")]
    pub id: String,
    pub user_id: String,
    pub device_name: String,
    pub device_type: String,
    #[serde(with = "datetime_format")]
    pub last_active: DateTime<Utc>,
    pub is_active: bool,
    #[serde(with = "datetime_format")]
    pub created_at: DateTime<Utc>,
}

/// Authentication service for user management and JWT security
pub struct AuthService {
    database: Arc<Database>,
    jwt_secret: String,
    token_expiry_hours: i64,
}

impl AuthService {
    /// Create a new AuthService instance
    pub async fn new(database: Arc<Database>, jwt_secret: String, token_expiry_hours: i64) -> PrismaResult<Self> {
        Ok(Self {
            database,
            jwt_secret,
            token_expiry_hours,
        })
    }

    /// Register a new user with email and password
    pub async fn register(&self, request: RegisterRequest) -> PrismaResult<AuthResponse> {
        info!("Attempting to register user with email: {}", request.email);

        // Check if user already exists
        if self.get_user_by_email(&request.email).await?.is_some() {
            return Err(PrismaError::from(DomainError::ValidationError(
                "User with this email already exists".to_string()
            )));
        }

        // Validate email format
        if !self.is_valid_email(&request.email) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "Invalid email format".to_string()
            )));
        }

        // Validate password strength
        if !self.is_valid_password(&request.password) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "Password must be at least 8 characters long".to_string()
            )));
        }

        // Hash the password
        let password_hash = hash(&request.password, DEFAULT_COST)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to hash password: {}", e)
            )))?;

        // Create new user
        let user_id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let user = User {
            id: user_id.clone(),
            email: request.email.clone(),
            password_hash,
            role: "user".to_string(), // Use string representation
            is_active: true,
            email_verified: false, // TODO: Implement email verification
            created_at: now,
            updated_at: now,
            last_login: None,
        };

        // Store user in database
        self.create_user(&user).await?;

        // Register device if provided
        if let Some(device_id) = &request.device_id {
            self.register_device(&user_id, device_id, "Unknown", "Unknown").await?;
        }

        // Generate JWT token
        let auth_response = self.generate_auth_response(&user, request.device_id).await?;

        info!("Successfully registered user: {}", request.email);
        Ok(auth_response)
    }

    /// Authenticate user with email and password
    pub async fn login(&self, request: LoginRequest) -> PrismaResult<AuthResponse> {
        info!("Attempting to login user with email: {}", request.email);

        // Get user by email
        let user = self.get_user_by_email(&request.email).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "Invalid email or password".to_string()
            )))?;

        // Check if user is active
        if !user.is_active {
            return Err(PrismaError::from(DomainError::AuthError(
                "User account is deactivated".to_string()
            )));
        }

        // Verify password
        let password_valid = verify(&request.password, &user.password_hash)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Password verification failed: {}", e)
            )))?;

        if !password_valid {
            return Err(PrismaError::from(DomainError::AuthError(
                "Invalid email or password".to_string()
            )));
        }

        // Update last login time
        let mut updated_user = user.clone();
        updated_user.last_login = Some(Utc::now());
        updated_user.updated_at = Utc::now();
        self.update_user(&updated_user).await?;

        // Update device activity if provided
        if let Some(device_id) = &request.device_id {
            self.update_device_activity(&user.id, device_id).await?;
        }

        // Generate JWT token
        let auth_response = self.generate_auth_response(&updated_user, request.device_id).await?;

        info!("Successfully logged in user: {}", request.email);
        Ok(auth_response)
    }

    /// Validate JWT token and return user claims
    pub async fn validate_token(&self, token: &str) -> PrismaResult<Claims> {
        let decoding_key = DecodingKey::from_secret(self.jwt_secret.as_ref());
        let validation = Validation::new(Algorithm::HS256);

        let token_data = decode::<Claims>(token, &decoding_key, &validation)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Invalid token: {}", e)
            )))?;

        // Check if user still exists and is active
        let user = self.get_user_by_id(&token_data.claims.sub).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "User not found".to_string()
            )))?;

        if !user.is_active {
            return Err(PrismaError::from(DomainError::AuthError(
                "User account is deactivated".to_string()
            )));
        }

        Ok(token_data.claims)
    }

    /// Refresh JWT token
    pub async fn refresh_token(&self, refresh_token: &str) -> PrismaResult<AuthResponse> {
        // For simplicity, we'll validate the refresh token the same way as access token
        // In production, you might want separate refresh token handling
        let claims = self.validate_token(refresh_token).await?;

        let user = self.get_user_by_id(&claims.sub).await?
            .ok_or_else(|| PrismaError::from(DomainError::AuthError(
                "User not found".to_string()
            )))?;

        self.generate_auth_response(&user, claims.device_id).await
    }

    /// Get user by ID
    pub async fn get_user_by_id(&self, user_id: &str) -> PrismaResult<Option<User>> {
        // Remove hyphens from UUID to make it SurrealDB compatible
        let clean_id = user_id.replace("-", "");

        // Use raw SQL query to avoid deserialization issues
        // Use meta::id() to get the record ID as a string to avoid complex record ID serialization issues
        let query = format!("SELECT meta::id(id) as id, email, password_hash, role, is_active, email_verified, created_at, updated_at, last_login FROM user:{}", clean_id);

        let mut result = self.database.client()
            .query(query)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query user: {}", e)
            )))?;

        // Try to get the first result as a raw JSON value
        // Use a more flexible approach to handle SurrealDB response format
        let user_data: Option<serde_json::Value> = match result.take::<Option<serde_json::Value>>(0) {
            Ok(data) => data,
            Err(e) => {
                println!("🚨 Failed to parse user query result: {}", e);
                // Try alternative approach - get raw response
                return Ok(None);
            }
        };

        if let Some(data) = user_data {
            // Manually construct User from the JSON data
            if let Some(user_obj) = data.as_object() {
                let user = User {
                    id: user_obj.get("id")
                        .and_then(|v| v.as_str())
                        .unwrap_or_default()
                        .to_string(),
                    email: user_obj.get("email")
                        .and_then(|v| v.as_str())
                        .unwrap_or_default()
                        .to_string(),
                    password_hash: user_obj.get("password_hash")
                        .and_then(|v| v.as_str())
                        .unwrap_or_default()
                        .to_string(),
                    role: user_obj.get("role")
                        .and_then(|v| v.as_str())
                        .unwrap_or("user")
                        .to_string(),
                    is_active: user_obj.get("is_active")
                        .and_then(|v| v.as_bool())
                        .unwrap_or(true),
                    email_verified: user_obj.get("email_verified")
                        .and_then(|v| v.as_bool())
                        .unwrap_or(false),
                    created_at: user_obj.get("created_at")
                        .and_then(|v| v.as_str())
                        .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&Utc))
                        .unwrap_or_else(|| Utc::now()),
                    updated_at: user_obj.get("updated_at")
                        .and_then(|v| v.as_str())
                        .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&Utc))
                        .unwrap_or_else(|| Utc::now()),
                    last_login: user_obj.get("last_login")
                        .and_then(|v| v.as_str())
                        .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&Utc)),
                };
                Ok(Some(user))
            } else {
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    /// Get user by email
    pub async fn get_user_by_email(&self, email: &str) -> PrismaResult<Option<User>> {
        // Use a simpler approach - query for specific fields to avoid enum issues
        // Use meta::id() to get the record ID as a string to avoid complex record ID serialization issues
        let query = "SELECT meta::id(id) as id, email, password_hash, role, is_active, email_verified, created_at, updated_at, last_login FROM user WHERE email = $email";

        let mut params = std::collections::BTreeMap::new();
        params.insert("email", serde_json::Value::String(email.to_string()));

        println!("🔍 Executing user query: {}", query);
        println!("🔍 With email: {}", email);

        let mut result = self.database.client()
            .query(query)
            .bind(params)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query users: {}", e)
            )))?;

        // Try to get the first result as a raw string and parse manually
        let raw_result: Option<String> = match result.take::<Option<serde_json::Value>>(0) {
            Ok(data) => {
                println!("🔍 Raw query result: {:?}", data);
                // If we can't parse as JSON, try to extract as string
                if let Some(json_data) = data {
                    if let Some(arr) = json_data.as_array() {
                        if let Some(first) = arr.first() {
                            Some(first.to_string())
                        } else {
                            None
                        }
                    } else {
                        Some(json_data.to_string())
                    }
                } else {
                    None
                }
            },
            Err(e) => {
                println!("🚨 Failed to parse user query result: {}", e);
                return Ok(None);
            }
        };

        if let Some(raw_data) = raw_result {
            println!("🔍 Raw user data: {}", raw_data);

            // Parse the JSON string to extract the actual user data
            if let Ok(user_json) = serde_json::from_str::<serde_json::Value>(&raw_data) {
                if let Some(user_obj) = user_json.as_object() {
                    let user = User {
                        id: user_obj.get("id")
                            .and_then(|v| v.as_str())
                            .unwrap_or("unknown-id")
                            .to_string(),
                        email: user_obj.get("email")
                            .and_then(|v| v.as_str())
                            .unwrap_or_default()
                            .to_string(),
                        password_hash: user_obj.get("password_hash")
                            .and_then(|v| v.as_str())
                            .unwrap_or_default()
                            .to_string(),
                        role: user_obj.get("role")
                            .and_then(|v| v.as_str())
                            .unwrap_or("user")
                            .to_string(),
                        is_active: user_obj.get("is_active")
                            .and_then(|v| v.as_bool())
                            .unwrap_or(true),
                        email_verified: user_obj.get("email_verified")
                            .and_then(|v| v.as_bool())
                            .unwrap_or(false),
                        created_at: user_obj.get("created_at")
                            .and_then(|v| v.as_str())
                            .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                            .map(|dt| dt.with_timezone(&Utc))
                            .unwrap_or_else(|| Utc::now()),
                        updated_at: user_obj.get("updated_at")
                            .and_then(|v| v.as_str())
                            .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                            .map(|dt| dt.with_timezone(&Utc))
                            .unwrap_or_else(|| Utc::now()),
                        last_login: user_obj.get("last_login")
                            .and_then(|v| v.as_str())
                            .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                            .map(|dt| dt.with_timezone(&Utc)),
                    };
                    println!("✅ Found user with email: {} and password hash: {}", email, &user.password_hash[..20]);
                    Ok(Some(user))
                } else {
                    println!("❌ Failed to parse user data as JSON object");
                    Ok(None)
                }
            } else {
                println!("❌ Failed to parse raw data as JSON");
                Ok(None)
            }
        } else {
            println!("❌ No data returned from query");
            Ok(None)
        }
    }

    /// Create a new user in the database
    async fn create_user(&self, user: &User) -> PrismaResult<()> {
        // Use raw SQL query to avoid any serialization issues
        // Remove hyphens from UUID to make it SurrealDB compatible
        let clean_id = user.id.replace("-", "");
        let query = format!(
            "CREATE user:{} SET email = $email, password_hash = $password_hash, role = $role, is_active = $is_active, email_verified = $email_verified, created_at = $created_at, updated_at = $updated_at, last_login = $last_login",
            clean_id
        );

        let mut params = std::collections::BTreeMap::new();
        params.insert("email", serde_json::Value::String(user.email.clone()));
        params.insert("password_hash", serde_json::Value::String(user.password_hash.clone()));
        params.insert("role", serde_json::Value::String(user.role.clone()));
        params.insert("is_active", serde_json::Value::Bool(user.is_active));
        params.insert("email_verified", serde_json::Value::Bool(user.email_verified));
        params.insert("created_at", serde_json::Value::String(user.created_at.to_rfc3339()));
        params.insert("updated_at", serde_json::Value::String(user.updated_at.to_rfc3339()));
        params.insert("last_login", match user.last_login {
            Some(dt) => serde_json::Value::String(dt.to_rfc3339()),
            None => serde_json::Value::Null,
        });

        println!("🔍 About to execute SurrealDB query: {}", query);
        println!("🔍 With params: {:?}", params);

        // Use raw query to avoid any type issues
        // Just execute the query and check for errors, don't try to parse the result
        let _result = self.database.client()
            .query(query)
            .bind(params)
            .await
            .map_err(|e| {
                println!("🚨 SurrealDB query error: {}", e);
                PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to create user: {}", e)
                ))
            })?;

        println!("✅ User creation query executed successfully in SurrealDB");
        Ok(())
    }

    /// Update user in the database
    async fn update_user(&self, user: &User) -> PrismaResult<()> {
        println!("🔍 About to update user with ID: {}", user.id);
        println!("🔍 User role: {}", user.role);
        println!("🔍 User last_login: {:?}", user.last_login);

        // Remove hyphens from UUID to make it SurrealDB compatible
        let clean_id = user.id.replace("-", "");

        // Use BTreeMap instead of serde_json::json! to avoid any potential enum issues
        let mut params = std::collections::BTreeMap::new();
        params.insert("email", serde_json::Value::String(user.email.clone()));
        params.insert("password_hash", serde_json::Value::String(user.password_hash.clone()));
        params.insert("role", serde_json::Value::String(user.role.clone()));
        params.insert("is_active", serde_json::Value::Bool(user.is_active));
        params.insert("email_verified", serde_json::Value::Bool(user.email_verified));
        params.insert("created_at", serde_json::Value::String(user.created_at.to_rfc3339()));
        params.insert("updated_at", serde_json::Value::String(user.updated_at.to_rfc3339()));
        params.insert("last_login", match user.last_login {
            Some(dt) => serde_json::Value::String(dt.to_rfc3339()),
            None => serde_json::Value::Null,
        });

        println!("🔍 About to execute SurrealDB update with params: {:?}", params);

        // Since UPDATE is causing issues, use DELETE + CREATE approach which we know works
        // First delete the existing record
        let delete_query = format!("DELETE user:{}", clean_id);
        println!("🔍 About to execute SurrealDB DELETE query: {}", delete_query);

        let _delete_result = self.database.client()
            .query(&delete_query)
            .await
            .map_err(|e| {
                println!("❌ SurrealDB delete failed: {}", e);
                PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to delete user for update: {}", e)
                ))
            })?;

        println!("✅ User deleted successfully, now recreating with updated data");

        // Now create the record with updated data (reuse the same logic as create_user)
        let create_query = format!(
            "CREATE user:{} SET email = $email, password_hash = $password_hash, role = $role, is_active = $is_active, email_verified = $email_verified, created_at = $created_at, updated_at = $updated_at, last_login = $last_login",
            clean_id
        );

        let mut params = std::collections::BTreeMap::new();
        params.insert("email", serde_json::Value::String(user.email.clone()));
        params.insert("password_hash", serde_json::Value::String(user.password_hash.clone()));
        params.insert("role", serde_json::Value::String(user.role.clone()));
        params.insert("is_active", serde_json::Value::Bool(user.is_active));
        params.insert("email_verified", serde_json::Value::Bool(user.email_verified));
        params.insert("created_at", serde_json::Value::String(user.created_at.to_rfc3339()));
        params.insert("updated_at", serde_json::Value::String(user.updated_at.to_rfc3339()));
        params.insert("last_login", match user.last_login {
            Some(dt) => serde_json::Value::String(dt.to_rfc3339()),
            None => serde_json::Value::Null,
        });

        println!("🔍 About to execute SurrealDB CREATE query: {}", create_query);
        println!("🔍 With params: {:?}", params);

        let _result = self.database.client()
            .query(create_query)
            .bind(params)
            .await
            .map_err(|e| {
                println!("❌ SurrealDB create failed: {}", e);
                PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to recreate user: {}", e)
                ))
            })?;

        println!("✅ User update successful");
        Ok(())
    }

    /// Register a device for multi-device support
    async fn register_device(&self, user_id: &str, device_id: &str, device_name: &str, device_type: &str) -> PrismaResult<()> {
        let now = Utc::now();

        // Use raw SQL query to avoid any serialization issues
        let query = format!(
            "CREATE device:{} SET user_id = $user_id, device_name = $device_name, device_type = $device_type, last_active = $last_active, is_active = $is_active, created_at = $created_at",
            device_id
        );

        let mut params = std::collections::BTreeMap::new();
        params.insert("user_id", serde_json::Value::String(user_id.to_string()));
        params.insert("device_name", serde_json::Value::String(device_name.to_string()));
        params.insert("device_type", serde_json::Value::String(device_type.to_string()));
        params.insert("last_active", serde_json::Value::String(now.to_rfc3339()));
        params.insert("is_active", serde_json::Value::Bool(true));
        params.insert("created_at", serde_json::Value::String(now.to_rfc3339()));

        // Use raw query to avoid any type issues
        // Just execute the query and check for errors, don't try to parse the result
        let _result = self.database.client()
            .query(query)
            .bind(params)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to create device: {}", e)
            )))?;

        Ok(())
    }

    /// Update device activity timestamp
    async fn update_device_activity(&self, user_id: &str, device_id: &str) -> PrismaResult<()> {
        // Use raw SQL query to check if device exists
        let query = format!("SELECT * FROM device:{} WHERE user_id = $user_id", device_id);

        let mut params = std::collections::BTreeMap::new();
        params.insert("user_id", serde_json::Value::String(user_id.to_string()));

        let mut result = self.database.client()
            .query(query)
            .bind(params)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query device: {}", e)
            )))?;

        // Check if device exists
        let device_exists: Option<serde_json::Value> = match result.take::<Option<serde_json::Value>>(0) {
            Ok(data) => data,
            Err(_) => None,
        };

        if device_exists.is_some() {
            // Update existing device activity
            let now = Utc::now();
            let update_query = format!("UPDATE device:{} SET last_active = $last_active, is_active = $is_active", device_id);

            let mut update_params = std::collections::BTreeMap::new();
            update_params.insert("last_active", serde_json::Value::String(now.to_rfc3339()));
            update_params.insert("is_active", serde_json::Value::Bool(true));

            let _result = self.database.client()
                .query(update_query)
                .bind(update_params)
                .await
                .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                    format!("Failed to update device: {}", e)
                )))?;
        } else {
            // Create new device if it doesn't exist
            self.register_device(user_id, device_id, "Unknown", "Unknown").await?;
        }

        Ok(())
    }

    /// Generate authentication response with JWT tokens
    async fn generate_auth_response(&self, user: &User, device_id: Option<String>) -> PrismaResult<AuthResponse> {
        let now = Utc::now();
        let exp = now + Duration::hours(self.token_expiry_hours);

        let claims = Claims {
            sub: user.id.clone(),
            email: user.email.clone(),
            role: match user.role.as_str() {
                "admin" => UserRole::Admin,
                "guest" => UserRole::Guest,
                _ => UserRole::User,
            },
            exp: exp.timestamp(),
            iat: now.timestamp(),
            device_id,
        };

        let encoding_key = EncodingKey::from_secret(self.jwt_secret.as_ref());
        let header = Header::new(Algorithm::HS256);

        let token = encode(&header, &claims, &encoding_key)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to generate token: {}", e)
            )))?;

        // For simplicity, use the same token as refresh token
        // In production, you might want separate refresh token logic
        let refresh_token = token.clone();

        let user_info = UserInfo {
            id: user.id.clone(),
            email: user.email.clone(),
            role: user.role.clone(),
            is_active: user.is_active,
            email_verified: user.email_verified,
            created_at: user.created_at,
            last_login: user.last_login,
        };

        Ok(AuthResponse {
            token,
            refresh_token,
            user: user_info,
            expires_at: exp,
        })
    }

    /// Validate email format
    fn is_valid_email(&self, email: &str) -> bool {
        email.contains('@') && email.contains('.') && email.len() > 5
    }

    /// Validate password strength
    fn is_valid_password(&self, password: &str) -> bool {
        password.len() >= 8
    }

    /// Deactivate user account
    pub async fn deactivate_user(&self, user_id: &str) -> PrismaResult<()> {
        let mut user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "User not found".to_string()
            )))?;

        user.is_active = false;
        user.updated_at = Utc::now();
        self.update_user(&user).await?;

        info!("Deactivated user: {}", user_id);
        Ok(())
    }

    /// Change user password
    pub async fn change_password(&self, user_id: &str, old_password: &str, new_password: &str) -> PrismaResult<()> {
        let user = self.get_user_by_id(user_id).await?
            .ok_or_else(|| PrismaError::from(DomainError::ValidationError(
                "User not found".to_string()
            )))?;

        // Verify old password
        let password_valid = verify(old_password, &user.password_hash)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Password verification failed: {}", e)
            )))?;

        if !password_valid {
            return Err(PrismaError::from(DomainError::AuthError(
                "Invalid current password".to_string()
            )));
        }

        // Validate new password
        if !self.is_valid_password(new_password) {
            return Err(PrismaError::from(DomainError::ValidationError(
                "New password must be at least 8 characters long".to_string()
            )));
        }

        // Hash new password
        let new_password_hash = hash(new_password, DEFAULT_COST)
            .map_err(|e| PrismaError::from(DomainError::AuthError(
                format!("Failed to hash new password: {}", e)
            )))?;

        // Update user
        let mut updated_user = user;
        updated_user.password_hash = new_password_hash;
        updated_user.updated_at = Utc::now();
        self.update_user(&updated_user).await?;

        info!("Changed password for user: {}", user_id);
        Ok(())
    }

    /// Get user devices
    pub async fn get_user_devices(&self, user_id: &str) -> PrismaResult<Vec<Device>> {
        // Use raw SQL query to avoid deserialization issues
        let query = "SELECT * FROM device WHERE user_id = $user_id";

        let mut params = std::collections::BTreeMap::new();
        params.insert("user_id", serde_json::Value::String(user_id.to_string()));

        let mut result = self.database.client()
            .query(query)
            .bind(params)
            .await
            .map_err(|e| PrismaError::from(DomainError::DatabaseError(
                format!("Failed to query devices: {}", e)
            )))?;

        // Get all results as raw JSON values
        let devices_data: Vec<serde_json::Value> = match result.take::<Vec<serde_json::Value>>(0) {
            Ok(data) => data,
            Err(e) => {
                println!("🚨 Failed to parse devices query result: {}", e);
                return Ok(Vec::new());
            }
        };

        let mut devices = Vec::new();
        for device_data in devices_data {
            if let Some(device_obj) = device_data.as_object() {
                let device = Device {
                    id: device_obj.get("id")
                        .and_then(|v| v.as_str())
                        .unwrap_or("unknown-id")
                        .to_string(),
                    user_id: device_obj.get("user_id")
                        .and_then(|v| v.as_str())
                        .unwrap_or_default()
                        .to_string(),
                    device_name: device_obj.get("device_name")
                        .and_then(|v| v.as_str())
                        .unwrap_or("Unknown")
                        .to_string(),
                    device_type: device_obj.get("device_type")
                        .and_then(|v| v.as_str())
                        .unwrap_or("Unknown")
                        .to_string(),
                    last_active: device_obj.get("last_active")
                        .and_then(|v| v.as_str())
                        .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&Utc))
                        .unwrap_or_else(|| Utc::now()),
                    is_active: device_obj.get("is_active")
                        .and_then(|v| v.as_bool())
                        .unwrap_or(true),
                    created_at: device_obj.get("created_at")
                        .and_then(|v| v.as_str())
                        .and_then(|s| DateTime::parse_from_rfc3339(s).ok())
                        .map(|dt| dt.with_timezone(&Utc))
                        .unwrap_or_else(|| Utc::now()),
                };
                devices.push(device);
            }
        }

        Ok(devices)
    }
}
